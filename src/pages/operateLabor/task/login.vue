<template>
  <div class="login-page">
    <div style="text-align: center; margin-top: 40px; padding: 40px">Logo</div>
    <Form style="display: flex; flex-direction: column; gap: 10px">
      <Field
        v-model="loginForm.phone"
        type="tel"
        placeholder="请通过手机号登录"
        maxlength="11"
        left-icon="phone-o"
        clearable
        style="background: #f2f2f2"
      />

      <Captcha v-model="loginForm.captcha" />

      <Field
        v-model="loginForm.smsCode"
        type="text"
        placeholder="请输入验证码"
        maxlength="6"
        left-icon="shield-o"
        clearable
        style="background: #f2f2f2"
      >
        <template #button>
          <a
            size="small"
            type="text"
            :disabled="smsCountdown > 0"
            @click="sendSmsCode"
            class="sms-btn"
            style="color: #4285f4; cursor: pointer"
          >
            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
          </a>
        </template>
      </Field>

      <Button
        type="primary"
        size="large"
        :disabled="!canLogin"
        @click="handleLogin"
        block
        class="login-btn"
      >
        登录
      </Button>

      <div class="agreement-section">
        <Checkbox v-model="agreedToTerms" class="agreement-checkbox">
          <span class="agreement-text">
            阅读并同意
            <span class="agreement-link" @click="showAgreement">
              《隐私数据协议》
            </span>
            <span class="agreement-link" @click="showUserAgreement">
              《用户服务协议》
            </span>
          </span>
        </Checkbox>
      </div>
    </Form>

    <!-- 协议弹窗 -->
    <Popup
      v-model="agreementVisible"
      :style="{
        width: '90%',
        height: '70%',
        padding: '10px',
        display: 'flex',
        flexDirection: 'column'
      }"
      round
      close-icon-position="top-right"
    >
      <div class="agreement-body">
        <iframe
          src="/agreement.html"
          frameborder="0"
          width="100%"
          height="100%"
          style="padding: 0; margin: 0"
        ></iframe>
      </div>
      <Button
        type="primary"
        size="large"
        @click="confirmRead"
        block
        class="read-btn"
        style="height: 60px; flex: 0 0 60px"
      >
        已阅读
      </Button>
    </Popup>
  </div>
</template>

<script>
import { Form, Field, Button, Checkbox, Popup, Toast } from 'vant'
import Captcha from './captcha.vue'

export default {
  name: 'TaskLogin',

  components: {
    Form,
    Field,
    Button,
    Checkbox,
    Popup,
    Toast,
    Captcha
  },

  data() {
    return {
      loginForm: {
        phone: '',
        captcha: {},
        smsCode: ''
      },
      agreedToTerms: false,
      smsCountdown: 0,
      smsTimer: null,
      agreementVisible: false,
      agreementTitle: ''
    }
  },

  created() {},

  computed: {
    canLogin() {
      return (
        this.loginForm.phone.length === 11 &&
        this.loginForm.captcha.value &&
        this.loginForm.captcha.value.length === 4 &&
        this.loginForm.smsCode.length === 6 &&
        this.agreedToTerms
      )
    }
  },

  beforeDestroy() {
    if (this.smsTimer) {
      clearInterval(this.smsTimer)
    }
  },

  methods: {
    sendSmsCode() {
      if (this.loginForm.phone.length !== 11) {
        Toast('请输入正确的手机号')
        return
      }
      if (
        !this.loginForm.captcha.value ||
        this.loginForm.captcha.value.length !== 4
      ) {
        Toast('请输入正确的图形验证码')
        return
      }

      // TODO: 发送短信验证码
      console.log('发送短信验证码')

      // 开始倒计时
      this.smsCountdown = 60
      this.smsTimer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(this.smsTimer)
          this.smsTimer = null
        }
      }, 1000)
    },

    handleLogin() {
      if (!this.canLogin) return

      // TODO: 执行登录逻辑
      console.log('登录', this.loginForm)

      // 跳转到实名认证页面
      this.$router.push('/task/ocr')
    },

    showAgreement() {
      this.agreementTitle = '隐私数据协议'
      this.agreementVisible = true
    },

    showUserAgreement() {
      this.agreementTitle = '用户服务协议'
      this.agreementVisible = true
    },

    closeAgreement() {
      this.agreementVisible = false
    },

    confirmRead() {
      this.agreedToTerms = true
      this.closeAgreement()
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.login-page {
  min-height: 100vh;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-section {
  margin-bottom: 60px;
  margin-top: 40px;
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  position: relative;
}

.logo-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.logo-waves {
  position: relative;
  width: 60%;
  height: 60%;
}

.wave {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
}

.wave1 {
  width: 100%;
  height: 100%;
  animation: wave 2s infinite;
}

.wave2 {
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
  animation: wave 2s infinite 0.5s;
}

.wave3 {
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
  animation: wave 2s infinite 1s;
}

@keyframes wave {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.login-form {
  width: 100%;
  max-width: 320px;
}

.input-group {
  margin-bottom: 16px;
}

.captcha-image {
  width: 80px;
  height: 32px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.captcha-text {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  letter-spacing: 2px;
}

.sms-btn {
  font-size: 12px;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
}

.password-login {
  text-align: right;
  margin-bottom: 30px;
}

.password-login span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.login-btn {
  margin-bottom: 20px;
  border-radius: 25px;
  height: 50px;
  font-size: 18px;
  font-weight: 600;
}

.agreement-section {
  text-align: center;
}

.agreement-checkbox {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.agreement-text {
  margin-left: 8px;
}

.agreement-link {
  color: #87ceeb;
  text-decoration: underline;
  cursor: pointer;
}

/* Vant组件样式覆盖 */
:deep(.van-field) {
  align-items: center;
}

:deep(.van-field) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25px;
  margin-bottom: 16px;
  padding: 0 20px;
}

:deep(.van-field__control) {
  font-size: 16px;
  color: #333;
}

:deep(.van-field__control::placeholder) {
  color: #999;
}

:deep(.van-field__left-icon) {
  margin-right: 10px;
  font-size: 18px;
}

:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
}

:deep(.van-button--primary:disabled) {
  background: #ccc;
  border-color: #ccc;
}

:deep(.van-checkbox__icon) {
  border-color: rgba(255, 255, 255, 0.6);
}

:deep(.van-checkbox__icon--checked) {
  background: #4285f4;
  border-color: #4285f4;
}

.agreement-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  padding: 20px 20px 0 20px;
  text-align: center;
}

.agreement-header h3 {
  margin: 0;
  font-size: 18px;
  color: #323233;
  font-weight: 600;
}

.agreement-body {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.agreement-footer {
  padding: 0 20px 20px 20px;
}

.read-btn {
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}
</style>
