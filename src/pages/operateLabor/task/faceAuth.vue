<template>
  <div class="face-auth-page">
    <div class="header">
      <h2>人脸识别验证</h2>
      <p class="subtitle">请按照提示完成人脸识别</p>
    </div>

    <div class="face-section">
      <!-- 人脸识别区域 -->
      <div class="face-capture-area">
        <div class="face-frame">
          <div class="face-placeholder" v-if="!stream">
            <div class="avatar-icon">👤</div>
          </div>
          <video
            ref="videoElement"
            v-show="stream"
            autoplay
            muted
            playsinline
            class="video-preview"
          ></video>
          <canvas
            ref="canvasElement"
            style="display: none;"
            width="200"
            height="200"
          ></canvas>
          <div class="scan-border">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>
          <div class="countdown" v-if="countdown > 0">{{ countdown }}s</div>
        </div>
      </div>

      <!-- 提示文字 -->
      <div class="instructions">
        <h3>请录制一段眨眼视频</h3>
        <div class="steps">
          <div class="step">1. 正脸对框</div>
          <div class="step">2. 点击录制按钮开始眨眼</div>
          <div class="step">3. 录制完成后提交</div>
        </div>
      </div>

      <!-- 录制按钮 -->
      <div class="record-section">
        <Button
          class="record-btn"
          :class="{ recording: isRecording }"
          @click="toggleRecording"
          :disabled="isProcessing"
        >
          {{ recordButtonText }}
        </Button>
      </div>

      <!-- 协议勾选 -->
      <div class="agreement-section">
        <label class="agreement-checkbox">
          <input v-model="agreedToFaceAuth" type="checkbox" />
          <span class="checkmark"></span>
          <span class="agreement-text">
            阅读并同意
            <span class="agreement-link" @click="showFaceAuthAgreement"
              >《人脸识别服务规则》</span
            >
          </span>
        </label>
      </div>
    </div>

    <!-- 下一步按钮 -->
    <div class="button-section">
      <Button class="next-btn" :disabled="!canProceed" @click="handleNext">
        下一步
      </Button>
    </div>

    <!-- 协议弹窗 -->
    <div
      v-if="agreementVisible"
      class="agreement-modal"
      @click="closeAgreement"
    >
      <div class="agreement-content" @click.stop>
        <div class="agreement-header">
          <h3>人脸识别服务规则</h3>
          <button class="close-btn" @click="closeAgreement">×</button>
        </div>
        <div class="agreement-body">
          <div class="agreement-text-content">
            <p>为了保障您的账户安全，我们需要进行人脸识别验证。</p>
            <p>在使用人脸识别服务时，我们将：</p>
            <ul>
              <li>收集您的面部特征信息用于身份验证</li>
              <li>严格保护您的生物识别信息安全</li>
              <li>不会将您的面部信息用于其他用途</li>
              <li>您可以随时撤销授权</li>
            </ul>
            <p>请确保在光线充足的环境下进行人脸识别，以获得最佳识别效果。</p>
          </div>
        </div>
        <div class="agreement-footer">
          <Button class="read-btn" @click="confirmFaceAuthRead">已阅读</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Button, Toast } from 'vant'

export default {
  name: 'TaskFaceAuth',
  components: {
    Button
  },

  data() {
    return {
      isRecording: false,
      isProcessing: false,
      countdown: 0,
      countdownTimer: null,
      agreedToFaceAuth: false,
      agreementVisible: false,
      hasRecorded: false,
      // 摄像头相关
      stream: null,
      mediaRecorder: null,
      recordedChunks: [],
      videoElement: null,
      canvasElement: null
    }
  },

  computed: {
    recordButtonText() {
      if (this.isProcessing) return '处理中...'
      if (this.isRecording) return '录制中...'
      if (this.hasRecorded) return '重新录制'
      return '录制本人人脸'
    },

    canProceed() {
      return this.hasRecorded && this.agreedToFaceAuth
    }
  },

  async mounted() {
    // 初始化摄像头
    await this.initCamera()
  },

  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
    // 清理摄像头资源
    this.cleanup()
  },

  methods: {
    toggleRecording() {
      if (this.isRecording) {
        this.stopRecording()
      } else {
        this.startRecording()
      }
    },

    async startRecording() {
      try {
        if (!this.stream) {
          await this.initCamera()
        }

        if (!this.stream) {
          this.$message.error('无法访问摄像头，请检查权限设置')
          return
        }

        console.log('开始录制人脸')

        // 清空之前的录制数据
        this.recordedChunks = []

        // 创建MediaRecorder
        this.mediaRecorder = new MediaRecorder(this.stream, {
          mimeType: 'video/webm;codecs=vp9'
        })

        // 监听录制数据
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.recordedChunks.push(event.data)
          }
        }

        // 录制完成处理
        this.mediaRecorder.onstop = () => {
          this.processRecordedVideo()
        }

        // 开始录制
        this.mediaRecorder.start(100) // 每100ms收集一次数据
        this.isRecording = true
        this.countdown = 5 // 5秒录制时间

        this.countdownTimer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            this.stopRecording()
          }
        }, 1000)

      } catch (error) {
        console.error('录制启动失败:', error)
        this.$message.error('录制启动失败，请重试')
      }
    },

    stopRecording() {
      console.log('停止录制')

      this.isRecording = false
      this.countdown = 0

      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }

      // 停止MediaRecorder
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop()
      }
    },

    showFaceAuthAgreement() {
      this.agreementVisible = true
    },

    closeAgreement() {
      this.agreementVisible = false
    },

    confirmFaceAuthRead() {
      this.agreedToFaceAuth = true
      this.closeAgreement()
    },

    // 初始化摄像头
    async initCamera() {
      try {
        const constraints = {
          video: {
            width: { ideal: 200 },
            height: { ideal: 200 },
            facingMode: 'user' // 前置摄像头
          },
          audio: false
        }

        this.stream = await navigator.mediaDevices.getUserMedia(constraints)

        // 将视频流绑定到video元素
        if (this.$refs.videoElement) {
          this.$refs.videoElement.srcObject = this.stream
        }

        console.log('摄像头初始化成功')
      } catch (error) {
        console.error('摄像头初始化失败:', error)

        let errorMessage = '无法访问摄像头'
        if (error.name === 'NotAllowedError') {
          errorMessage = '请允许访问摄像头权限'
        } else if (error.name === 'NotFoundError') {
          errorMessage = '未找到摄像头设备'
        } else if (error.name === 'NotSupportedError') {
          errorMessage = '浏览器不支持摄像头功能'
        }

        this.$message.error(errorMessage)
      }
    },

    // 处理录制完成的视频
    processRecordedVideo() {
      this.isProcessing = true

      try {
        // 创建视频blob
        const videoBlob = new Blob(this.recordedChunks, {
          type: 'video/webm'
        })

        console.log('录制的视频大小:', videoBlob.size, 'bytes')

        // 这里可以进行视频处理，比如：
        // 1. 上传到服务器
        // 2. 进行人脸检测
        // 3. 提取关键帧等

        // 模拟处理过程
        setTimeout(() => {
          this.isProcessing = false
          this.hasRecorded = true
          this.$message.success('录制完成')

          // 可以在这里调用API上传视频
          // this.uploadVideo(videoBlob)
        }, 2000)

      } catch (error) {
        console.error('视频处理失败:', error)
        this.isProcessing = false
        this.$message.error('视频处理失败，请重试')
      }
    },

    // 上传视频到服务器（示例方法）
    async uploadVideo(videoBlob) {
      try {
        const formData = new FormData()
        formData.append('video', videoBlob, 'face-auth.webm')
        formData.append('userId', 'current-user-id') // 替换为实际用户ID

        // 这里调用实际的上传API
        // const response = await fetch('/api/upload-face-video', {
        //   method: 'POST',
        //   body: formData
        // })

        console.log('视频上传准备完成')
      } catch (error) {
        console.error('视频上传失败:', error)
        this.$message.error('视频上传失败')
      }
    },

    // 清理资源
    cleanup() {
      // 停止摄像头流
      if (this.stream) {
        this.stream.getTracks().forEach(track => {
          track.stop()
        })
        this.stream = null
      }

      // 清理MediaRecorder
      if (this.mediaRecorder) {
        this.mediaRecorder = null
      }

      // 清理录制数据
      this.recordedChunks = []
    },

    handleNext() {
      if (!this.canProceed) return

      // TODO: 提交人脸认证数据
      console.log('提交人脸认证')

      // 跳转到完成页面或下一个流程
      this.$message.success('实名认证完成')

      // 这里可以跳转到主页面或其他页面
      // this.$router.push('/dashboard')
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.face-auth-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.header h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.face-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.face-capture-area {
  margin-bottom: 30px;
}

.face-frame {
  width: 200px;
  height: 200px;
  position: relative;
  margin: 0 auto;
}

.face-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.avatar-icon {
  font-size: 80px;
  color: rgba(255, 255, 255, 0.8);
}

.scan-border {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #4285f4;
}

.corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
  border-radius: 20px 0 0 0;
}

.corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
  border-radius: 0 20px 0 0;
}

.corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 20px;
}

.corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
  border-radius: 0 0 20px 0;
}

.countdown {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  font-weight: bold;
  color: #4285f4;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.instructions {
  text-align: center;
  margin-bottom: 30px;
}

.instructions h3 {
  font-size: 18px;
  color: #333;
  margin: 0 0 15px 0;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  font-size: 14px;
  color: #666;
  text-align: left;
}

.record-section {
  margin-bottom: 30px;
}

.record-btn {
  width: 200px;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.record-btn.recording {
  background: #f44336;
  animation: pulse 1s infinite;
}

.record-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.agreement-section {
  margin-bottom: 20px;
}

.agreement-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: #333;
  font-size: 14px;
}

.agreement-checkbox input {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
}

.agreement-checkbox input:checked + .checkmark {
  background: #4285f4;
  border-color: #4285f4;
}

.agreement-checkbox input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: -2px;
  left: 2px;
}

.agreement-link {
  color: #4285f4;
  text-decoration: underline;
  cursor: pointer;
}

.button-section {
  padding: 20px 0 40px 0;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.next-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.next-btn:not(:disabled):hover {
  background: #3367d6;
  transform: translateY(-1px);
}

.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.agreement-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.agreement-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
}

.agreement-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.agreement-text-content {
  line-height: 1.6;
  color: #333;
}

.agreement-text-content p {
  margin-bottom: 15px;
}

.agreement-text-content ul {
  padding-left: 20px;
  margin-bottom: 15px;
}

.agreement-text-content li {
  margin-bottom: 8px;
}

.agreement-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.read-btn {
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-size: 16px;
  cursor: pointer;
}
</style>
