<template>
  <div class="id-page">
    <div class="header">
      <h2>身份信息确认</h2>
      <p class="subtitle">请确认您的身份信息</p>
    </div>

    <div class="form-section">
      <van-form @submit="handleNext">
        <van-cell-group>
          <van-field
            v-model="formData.name"
            name="name"
            label="姓名"
            placeholder="请输入姓名"
            :rules="[{ required: true, message: '请输入姓名' }]"
            clearable
          />

          <van-field
            v-model="formData.idNumber"
            name="idNumber"
            label="身份证号"
            placeholder="请输入身份证号码"
            :rules="[
              { required: true, message: '请输入身份证号码' },
              { validator: validateIdNumber, message: '身份证号码格式不正确' }
            ]"
            clearable
          />

          <van-field
            v-model="formData.gender"
            name="gender"
            label="性别"
            placeholder="请选择性别"
            readonly
            clickable
            :rules="[{ required: true, message: '请选择性别' }]"
            @click="showGenderPicker = true"
          />

          <van-field
            v-model="formData.birthDate"
            name="birthDate"
            label="出生日期"
            placeholder="请选择出生日期"
            readonly
            clickable
            :rules="[{ required: true, message: '请选择出生日期' }]"
            @click="showBirthDatePicker = true"
          />

          <van-field
            v-model="formData.address"
            name="address"
            label="地址"
            placeholder="请输入地址"
            :rules="[{ required: true, message: '请输入地址' }]"
            clearable
          />

          <van-field
            v-model="formData.issuingAuthority"
            name="issuingAuthority"
            label="签发机关"
            placeholder="请输入签发机关"
            :rules="[{ required: true, message: '请输入签发机关' }]"
            clearable
          />

          <van-field
            v-model="validPeriodText"
            name="validPeriod"
            label="有效期"
            placeholder="请选择有效期"
            readonly
            clickable
            :rules="[{ required: true, message: '请选择有效期' }]"
            @click="showValidPeriodPicker = true"
          />
        </van-cell-group>

        <!-- 下一步按钮 -->
        <div class="button-section">
          <Button
            type="primary"
            size="large"
            :disabled="!canProceed"
            native-type="submit"
            block
          >
            下一步
          </Button>
        </div>
      </van-form>
    </div>

    <!-- 性别选择器 -->
    <van-popup v-model="showGenderPicker" position="bottom">
      <van-picker
        :columns="genderColumns"
        @confirm="onGenderConfirm"
        @cancel="showGenderPicker = false"
      />
    </van-popup>

    <!-- 出生日期选择器 -->
    <van-popup v-model="showBirthDatePicker" position="bottom">
      <van-datetime-picker
        v-model="birthDateValue"
        type="date"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onBirthDateConfirm"
        @cancel="showBirthDatePicker = false"
      />
    </van-popup>

    <!-- 有效期选择器 -->
    <van-popup v-model="showValidPeriodPicker" position="bottom">
      <van-datetime-picker
        v-model="validPeriodStartValue"
        type="date"
        :min-date="minDate"
        :max-date="maxValidDate"
        title="选择有效期开始日期"
        @confirm="onValidPeriodStartConfirm"
        @cancel="showValidPeriodPicker = false"
      />
    </van-popup>

    <!-- 有效期结束日期选择器 -->
    <van-popup v-model="showValidPeriodEndPicker" position="bottom">
      <van-datetime-picker
        v-model="validPeriodEndValue"
        type="date"
        :min-date="validPeriodStartValue"
        :max-date="maxValidDate"
        title="选择有效期结束日期"
        @confirm="onValidPeriodEndConfirm"
        @cancel="showValidPeriodEndPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  Form,
  Field,
  CellGroup,
  Button,
  Popup,
  Picker,
  DatetimePicker,
  Toast
} from 'vant'

export default {
  name: 'TaskId',

  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [DatetimePicker.name]: DatetimePicker
  },

  data() {
    return {
      formData: {
        name: '',
        idNumber: '',
        gender: '',
        birthDate: '',
        address: '',
        issuingAuthority: '',
        validPeriod: []
      },
      showGenderPicker: false,
      showBirthDatePicker: false,
      showValidPeriodPicker: false,
      showValidPeriodEndPicker: false,
      birthDateValue: new Date(),
      validPeriodStartValue: new Date(),
      validPeriodEndValue: new Date(),
      genderColumns: [
        { text: '男', value: 'male' },
        { text: '女', value: 'female' }
      ],
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      maxValidDate: new Date(2050, 11, 31)
    }
  },

  computed: {
    canProceed() {
      return (
        this.formData.name &&
        this.formData.idNumber &&
        this.formData.gender &&
        this.formData.birthDate &&
        this.formData.address &&
        this.formData.issuingAuthority &&
        this.formData.validPeriod &&
        this.formData.validPeriod.length === 2
      )
    },

    validPeriodText() {
      if (this.formData.validPeriod && this.formData.validPeriod.length === 2) {
        return `${this.formData.validPeriod[0]} 至 ${this.formData.validPeriod[1]}`
      }
      return ''
    }
  },

  created() {
    const { frontImage, backImage } = this.$route.query
    if (frontImage && backImage) {
      this.mockOcrData()
    }
  },

  methods: {
    validateIdNumber(value) {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      return reg.test(value)
    },

    mockOcrData() {
      this.formData = {
        name: '张三',
        idNumber: '110101199001011234',
        gender: '男',
        birthDate: '1990-01-01',
        address: '北京市朝阳区某某街道某某小区1号楼1单元101室',
        issuingAuthority: '北京市公安局朝阳分局',
        validPeriod: ['2020-01-01', '2030-01-01']
      }
    },

    onGenderConfirm(value) {
      this.formData.gender = value.text
      this.showGenderPicker = false
    },

    onBirthDateConfirm(value) {
      this.formData.birthDate = this.formatDate(value)
      this.showBirthDatePicker = false
    },

    onValidPeriodStartConfirm(value) {
      this.validPeriodStartValue = value
      this.showValidPeriodPicker = false
      this.showValidPeriodEndPicker = true
    },

    onValidPeriodEndConfirm(value) {
      this.validPeriodEndValue = value
      this.formData.validPeriod = [
        this.formatDate(this.validPeriodStartValue),
        this.formatDate(this.validPeriodEndValue)
      ]
      this.showValidPeriodEndPicker = false
    },

    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    handleNext() {
      if (!this.canProceed) {
        Toast('请完善表单信息')
        return
      }

      // TODO: 提交身份信息
      console.log('提交身份信息', this.formData)

      // 跳转到人脸认证页面
      this.$router.push({
        path: '/task/faceAuth',
        query: {
          name: this.formData.name,
          idNumber: this.formData.idNumber
        }
      })
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.id-page {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 0;
}

.header {
  text-align: center;
  padding: 20px 20px 30px 20px;
  background: white;
  margin-bottom: 10px;
}

.header h2 {
  font-size: 20px;
  color: #323233;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.form-section {
  background: white;
}

.button-section {
  padding: 20px;
  background: white;
  margin-top: 10px;
}

/* 自定义Vant组件样式 */
:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 80px;
  color: #323233;
  font-weight: 500;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-button--primary:disabled) {
  background: #c8c9cc;
  border-color: #c8c9cc;
}

:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-datetime-picker__toolbar) {
  padding: 16px;
}

:deep(.van-datetime-picker__title) {
  font-size: 16px;
  font-weight: 600;
}
</style>
