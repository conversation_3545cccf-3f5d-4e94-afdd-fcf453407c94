<template>
  <Field
    v-model="captcha.value"
    placeholder="请输入图形验证码"
    maxlength="4"
    clearable
    @input="emitValue"
    center
    style="background: #f2f2f2"
  >
    <template #button>
      <img
        v-if="captcha.token"
        :src="captchaImage"
        alt="captcha"
        @click="refreshCaptcha"
        class="captcha-img"
      />
    </template>
  </Field>
</template>

<script>
import { Field } from 'vant'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'TaskCaptcha',
  components: {
    Field
  },
  props: {
    value: {
      type: Object,
      default: () => ({ token: '', value: '' })
    }
  },
  data() {
    return {
      captcha: {
        token: '',
        value: ''
      }
    }
  },
  computed: {
    captchaImage() {
      // Ensure window.env.apiPath is available or provide a fallback
      const apiPath = window.env?.apiPath || ''
      return `${apiPath}/api/public/captcha?token=${encodeURIComponent(
        this.captcha.token
      )}`
    }
  },
  watch: {
    value: {
      handler(newValue) {
        // Sync from parent
        if (newValue && newValue.token !== this.captcha.token) {
          this.captcha.token = newValue.token
        }
        if (newValue && newValue.value !== this.captcha.value) {
          this.captcha.value = newValue.value
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.refreshCaptcha()
  },
  methods: {
    async refreshCaptcha() {
      const [err, r] = await client.createCaptcha()
      if (err) {
        handleError(err)
        return
      }
      this.captcha.token = r.data
      this.captcha.value = '' // Clear input on refresh
      this.emitValue()
    },
    emitValue() {
      this.$emit('input', this.captcha)
    }
  }
}
</script>

<style scoped>
.captcha-img {
  height: 24px; /* Match the typical height of Vant input content */
  vertical-align: middle;
  cursor: pointer;
}
</style>
