<template>
  <div class="taxPaymentVoucher">
    <el-form
      :inline="true"
      class="search-form"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="search-container">
        <div class="search-fields">
          <el-form-item label="作业主体" class="search-item">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              class="search-select"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="税款所属期" class="search-item">
            <el-date-picker
              v-model="conditions.filters.taxPaymentPeriod"
              type="month"
              placeholder="选择月份"
              value-format="yyyy-MM"
              class="search-date"
              clearable
            ></el-date-picker>
          </el-form-item>
        </div>

        <div class="search-buttons">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleUpload">
        上传完税证明
      </el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="loading"
        size="small"
        :data="data"
        class="responsive-table"
        :header-cell-style="{
          'font-size': '12px',
          'font-weight': '400',
          color: '#777c94',
          background: 'var(--o-primary-bg-color)'
        }"
      >
        <el-table-column
          prop="id"
          label="完税证明ID"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.id) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="supplierCorporationName"
          label="作业主体名称"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.supplierCorporationName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="taxPaymentPeriod"
          label="税款所属期"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.taxPaymentPeriod) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          min-width="180"
          label="上传日期"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div style="text-align: right; padding: 20px 0; flex: 0 0 auto">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
        :page-size="conditions.limit"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 上传完税证明对话框 -->
    <el-dialog
      title="上传完税证明"
      :visible.sync="uploadDialogVisible"
      width="650px"
      :close-on-click-modal="false"
    >
      
      <div style="border-radius: 6px; margin: 16px 0; text-align: center">
        <el-upload
          width="100%"
          :headers="headerToken"
          ref="upload"
          class="upload"
          drag
          :data="uploadData"
          :file-list="fileList"
          :auto-upload="false"
          :on-success="onSuccess"
          :on-change="onChange"
          :on-error="onError"
          :on-remove="onRemove"
          accept=".pdf, .png, .jpg, .jpeg"
          :action="uploadUrl"
          multiple
        >
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
            "
          >
            <i class="el-icon-upload" style="font-size: 67px; color: #c0c4cc; margin-bottom: 16px;"></i>
            <div style="color: #409eff; margin-bottom: 12px; font-size: 16px; font-weight: 500;">
              点击上传
            </div>
          </div>
        </el-upload>
        <div style="color: #999; font-size: 13px;margin-top: 20px;">
              支持上传 PDF、PNG、JPG 格式文件，单次上传文件总大小不超过 15M，支持多文件上传
        </div>
      </div>

      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadForm" label-width="150px">
        <el-form-item label="作业主体" prop="supplierCorporationId" required>
          <el-select
            filterable
            v-model="uploadForm.supplierCorporationId"
            placeholder="请选择作业主体"
            style="width: 300px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税款所属期" prop="taxPaymentPeriod" required>
          <el-date-picker
            v-model="uploadForm.taxPaymentPeriod"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            style="width: 300px"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelUpload">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpload"
          :loading="uploading"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'

const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          taxPaymentPeriod: '',
          supplierCorporationName: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: [],
      // 上传对话框相关
      uploadDialogVisible: false,
      uploading: false,
      fileList: [],
      uploadedFileIds: [],
      uploadForm: {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      },
      uploadRules: {
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: ['change', 'blur'] }
        ],
        taxPaymentPeriod: [
          { required: true, message: '请选择税款所属期', trigger: ['change', 'blur'] }
        ]
      },
      uploadData: {},
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env?.apiPath}/api/public/uploadFile`
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 格式化文本显示
    formatText(value) {
      return value || '-'
    },
    // 格式化金额显示
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions.filters = {
        id: '',
        supplierCorporationId: '',
        taxPaymentPeriod: '',
        supplierCorporationName: '',
        createTimeStart: null,
        createTimeEnd: null
      }
      this.getList()
    },
    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      const [err, r] = await client.taxPaymentVoucherList({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleUpload() {
      // 重置表单
      this.uploadForm = {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      }
      this.fileList = []
      this.uploadedFileIds = []
      this.$nextTick(() => {
        if (this.$refs.uploadForm) {
          this.$refs.uploadForm.clearValidate()
        }
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
      this.uploadDialogVisible = true
    },
    cancelUpload() {
      this.uploadDialogVisible = false
      this.fileList = []
      this.uploadedFileIds = []
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    async confirmUpload() {
      // 验证表单
      const valid = await new Promise(resolve => {
        this.$refs.uploadForm.validate(resolve)
      })

      if (!valid) {
        return
      }

      if (this.fileList.length === 0) {
        this.$message.error('请选择要上传的文件')
        return
      }

      // 检查文件总大小
      const totalSize = this.fileList.reduce((sum, file) => sum + file.size, 0)
      const maxSize = 15 * 1024 * 1024 // 15MB
      if (totalSize > maxSize) {
        this.$message.error('文件总大小不能超过15M')
        return
      }

      this.uploading = true

      try {
        // 上传所有文件
        for (const file of this.fileList) {
          if (file.status !== 'success') {
            await this.uploadSingleFile(file)
          }
        }

        // 调用保存接口
        const requestData = {
          id: 0,
          supplierCorporationId: this.uploadForm.supplierCorporationId,
          taxPaymentPeriod: this.uploadForm.taxPaymentPeriod,
          fileIds: this.uploadedFileIds.join(',')
        }

        const [err, response] = await client.addTaxPaymentVoucher({
          body: requestData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('完税证明上传成功')
          this.uploadDialogVisible = false
          // 重新查询列表
          await this.getList()
        } else {
          this.$message.error(response.message || '上传失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.uploading = false
      }
    },
    async uploadSingleFile(file) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file.raw)

        const xhr = new XMLHttpRequest()
        xhr.open('POST', this.uploadUrl)
        xhr.setRequestHeader('Authorization', this.headerToken.Authorization)

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              if (response.success && response.data) {
                this.uploadedFileIds.push(response.data.fileId.toString())
                file.status = 'success'
                resolve(response)
              } else {
                reject(new Error(response.message || '上传失败'))
              }
            } catch (e) {
              reject(new Error('解析响应失败'))
            }
          } else {
            reject(new Error(`上传失败: ${xhr.status}`))
          }
        }

        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }

        xhr.send(formData)
      })
    },
    onChange(file, fileList) {
      this.fileList = fileList
    },
    onRemove(file, fileList) {
      this.fileList = fileList
      // 从已上传文件ID列表中移除
      const index = this.uploadedFileIds.indexOf(file.response?.data?.toString())
      if (index > -1) {
        this.uploadedFileIds.splice(index, 1)
      }
    },
    onSuccess(response) {
      if (response && response.success && response.data) {
        this.uploadedFileIds.push(response.data.toString())
      }
    },
    onError(err, file) {
      console.error('文件上传错误：', err)
      this.$message.error(`文件 ${file.name} 上传失败`)
    },
    handleView(row) {
      this.$router.push(`/taxPaymentVoucher/${row.id}`)
    }
  }
}
</script>

<style scoped>
/* 主容器样式 */
.taxPaymentVoucher {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* 搜索表单响应式布局 */
.search-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  flex-wrap: wrap;
}

.search-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  flex: 1;
  min-width: 0;
}

.search-item {
  margin-right: 20px;
  margin-bottom: 20px;
}

.search-select,
.search-date {
  width: 280px;
}

.search-buttons {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  padding-top: 5px;
  flex-shrink: 0;
}

/* 表格容器 */
.table-container {
  flex: 1 1 auto;
  min-height: 0;
  overflow: hidden;
}

.responsive-table {
  width: 100%;
  height: 100%;
}

/* 上传对话框样式 */
.upload {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

/* 文件列表居中显示 */
.upload ::v-deep .el-upload-list {
  text-align: center;
}

.upload ::v-deep .el-upload-list__item {
  text-align: center;
  margin: 0 auto;
}

.upload ::v-deep .el-upload-list__item-name {
  text-align: center;
  display: block;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-select,
  .search-date {
    width: 240px;
  }
}

@media (max-width: 992px) {
  .search-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-buttons {
    justify-content: flex-end;
    padding-top: 0;
  }

  .search-select,
  .search-date {
    width: 220px;
  }
}

@media (max-width: 768px) {
  .search-fields {
    flex-direction: column;
  }

  .search-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .search-select,
  .search-date {
    width: 100%;
    max-width: 300px;
  }

  .search-buttons {
    justify-content: center;
  }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .search-select,
  .search-date {
    width: 320px;
  }
}

@media (min-width: 1600px) {
  .search-container {
    max-width: 1200px;
  }

  .search-select,
  .search-date {
    width: 350px;
  }
}
</style>
