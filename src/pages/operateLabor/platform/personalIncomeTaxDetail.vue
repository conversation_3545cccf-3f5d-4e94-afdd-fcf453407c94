<template>
  <div v-loading="loading">
    <!-- 作业主体名称和税款所属期 -->
    <div style="background: white; padding: 10px; margin-bottom: 20px;">
      <h1 style="font-size: 25px; font-weight: 600; color: #262626; margin: 0 0 12px 0; line-height: 1.2;">
        {{ taxData.supplierCorporationName }}
      </h1>
      <p style="font-size: 14px; color: #8c8c8c; margin: 0;">
        税款所属期：{{ taxData.taxPaymentPeriod }}
      </p>
    </div>

    <!-- 个税申报详情列表 -->
    <el-tabs v-model="activeTab" type="card" style="padding: 10px">
      <el-tab-pane label="申报详情" name="detail">
        <div style="padding: 10px;">
          <el-table
            v-loading="detailLoading"
            :data="detailData"
            style="width: 100%; margin-top: 10px;"
            :header-cell-style="{
              'font-size': '12px',
              'font-weight': '400',
              color: '#777c94',
              background: 'var(--o-primary-bg-color)'
            }"
          >
        <el-table-column
          prop="name"
          label="姓名"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.name) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="idCard"
          label="身份证号"
          width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.idCard) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="currentIncome"
          label="本期收入"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.currentIncome) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accumulatedIncome"
          label="累计收入"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.accumulatedIncome) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accumulatedExpenses"
          label="累计费用"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.accumulatedExpenses) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accumulatedTaxFreeIncome"
          label="累计免税收入"
          width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.accumulatedTaxFreeIncome) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accumulatedOtherDeductions"
          label="累计依法确定的其他扣除"
          width="190"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.accumulatedOtherDeductions) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accumulatedPrepaidTax"
          label="累计已预缴税额"
          width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.accumulatedPrepaidTax) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="currentWithholdingTax"
          label="本期应预扣预缴税额"
          width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.currentWithholdingTax) }}
          </template>
        </el-table-column>
      </el-table>

          <el-pagination
            v-if="detailTotal > 0"
            @current-change="handleDetailCurrentChange"
            :current-page="detailConditions.offset / detailConditions.limit + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="detailConditions.limit"
            layout="total, prev, pager, next"
            :total="detailTotal"
            style="text-align: right; margin-top: 10px"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      activeTab: 'detail',
      loading: true,
      detailLoading: false,
      taxData: {
        id: '',
        supplierCorporationName: '',
        taxPaymentPeriod: '',
        incomeTaxMonth: '',
        taxpayersCount: '',
        currentIncome: '',
        createTime: '',
        modifyTime: '',
        supplierId: '',
        currentWithholdingTax: ''
      },
      detailData: [],
      allDetailData: [], // 存储所有详情数据
      detailTotal: 0,
      detailConditions: {
        offset: 0,
        limit: 10
      }
    }
  },
  async created() {
    const id = this.$route.params.id
    if (id) {
      await this.loadTaxDetail(id)
    }
  },
  methods: {
    async loadTaxDetail(id) {
      this.loading = true
      try {
        const [err, response] = await client.queryPersonalTax({
          body: { id: parseInt(id) }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.taxData = response.data
          this.allDetailData = response.data.details || []
          this.detailTotal = this.allDetailData.length
          this.updateDetailData()
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },
    handleDetailCurrentChange(page) {
      this.detailConditions.offset = (page - 1) * this.detailConditions.limit
      this.updateDetailData()
    },
    updateDetailData() {
      const start = this.detailConditions.offset
      const end = start + this.detailConditions.limit
      this.detailData = this.allDetailData.slice(start, end)
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatText(text) {
      if (text === null || text === undefined || text === '') {
        return '-'
      }
      return text
    }
  }
}
</script>

<style scoped>
</style>
