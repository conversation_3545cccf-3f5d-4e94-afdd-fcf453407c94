<template>
  <div
    class="personalIncomeTax"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="税款所属期">
            <el-date-picker
              v-model="conditions.filters.taxPaymentPeriod"
              type="month"
              placeholder="选择月份"
              value-format="yyyy-MM"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleGenerate">
        生成申报表
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="个税申报表ID"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.id) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.supplierCorporationName) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="taxPaymentPeriod"
        label="税款所属期"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.taxPaymentPeriod) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="incomeTaxMonth"
        label="个税申报月"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.incomeTaxMonth) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="taxpayersCount"
        label="纳税人数"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.taxpayersCount) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="currentIncome"
        label="本期收入总额"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatAmount(scope.row.currentIncome) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="currentWithholdingTax"
        label="本期应预扣预缴税额"
        width="150"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ formatAmount(scope.row.currentWithholdingTax) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        width="150"
        label="生成日期"
      >
        <template slot-scope="scope">
          {{ formatText(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button type="text" size="small" @click="handleDownload(scope.row)"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <!-- 生成申报表对话框 -->
    <el-dialog
      title="生成申报表"
      :visible.sync="generateDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="generateForm" :rules="generateRules" ref="generateForm" label-width="100px">
        <el-form-item label="作业主体" prop="supplierCorporationId" required>
          <el-select
            filterable
            v-model="generateForm.supplierCorporationId"
            placeholder="请选择所属作业主体"
            style="width: 300px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税款所属期" prop="taxPaymentPeriod" required>
          <el-date-picker
            v-model="generateForm.taxPaymentPeriod"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            style="width: 300px"
            @change="validateTaxPeriod"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmGenerate"
          :loading="generating"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          taxPaymentPeriod: '',
          incomeTaxMonth: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: [],
      // 生成申报表对话框相关
      generateDialogVisible: false,
      generating: false,
      generateForm: {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      },
      generateRules: {
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: ['change', 'blur'] }
        ],
        taxPaymentPeriod: [
          { required: true, message: '请选择税款所属期', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          taxPaymentPeriod: '',
          incomeTaxMonth: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.getList()
    },
    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      const [err, r] = await client.personalTaxList({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleGenerate() {
      // 重置表单
      this.generateForm = {
        supplierCorporationId: '',
        taxPaymentPeriod: ''
      }
      this.$nextTick(() => {
        if (this.$refs.generateForm) {
          this.$refs.generateForm.clearValidate()
        }
      })
      this.generateDialogVisible = true
    },
    // 验证税款所属期
    validateTaxPeriod() {
      if (this.$refs.generateForm) {
        this.$refs.generateForm.validateField('taxPaymentPeriod')
      }
    },
    // 确认生成申报表
    async confirmGenerate() {
      // 表单验证
      const valid = await new Promise((resolve) => {
        this.$refs.generateForm.validate((valid) => {
          resolve(valid)
        })
      })

      if (!valid) {
        return
      }

      // 额外验证确保数据完整
      if (!this.generateForm.supplierCorporationId) {
        this.$message.error('请选择作业主体')
        return
      }

      if (!this.generateForm.taxPaymentPeriod) {
        this.$message.error('请选择税款所属期')
        return
      }

      this.generating = true

      try {
        const requestData = {
          id: 0,
          supplierCorporationId: this.generateForm.supplierCorporationId,
          taxPaymentPeriod: this.generateForm.taxPaymentPeriod,
        }

        const [err, response] = await client.addPersonalTax({
          body: requestData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('申报表生成成功')
          this.generateDialogVisible = false
          // 重新查询列表
          await this.getList()
        } else {
          this.$message.error(response.message || '生成失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.generating = false
      }
    },
    handleView(row) {
      this.$router.push(`/personalIncomeTax/${row.id}`)
    },
    handleDownload(row) {
      this.$message.info('下载功能待开发')
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatText(text) {
      if (text === null || text === undefined || text === '') {
        return '-'
      }
      return text
    }
  }
}
</script>

<style scoped>
</style>
