<template>
  <div
    :style="{
      display: 'flex',
      fontFamily: 'sans-serif',
      backgroundColor: '#F7F8FA',
      height: '100vh'
    }"
  >
    <slogo />

    <!-- Right Panel -->
    <div
      :style="{
        flex: '1',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
      }"
    >
      <!-- Header Links -->
      <div
        :style="{
          position: 'absolute',
          top: '20px',
          right: '40px',
          display: 'flex',
          alignItems: 'center'
        }"
      >
        <a
          @click="goToWebsite"
          :style="{
            color: '#666',
            textDecoration: 'none',
            cursor: 'pointer',
            margin: '0 15px'
          }"
          >返回官网</a
        >
      </div>

      <!-- Login Form -->
      <div
        :style="{
          width: '420px',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 10px 40px rgba(0,0,0,0.05)'
        }"
      >
        <h2
          :style="{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center',
            margin: '0 0 30px 0'
          }"
        >
          登录
        </h2>

        <div
          :style="{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'baseline',
            marginBottom: '25px'
          }"
        >
          <span :style="{ fontSize: '18px', color: '#333' }">验证码登录</span>
          <a
            @click="switchLoginType"
            :style="{
              fontSize: '14px',
              color: 'var(--o-primary-color)',
              cursor: 'pointer',
              textDecoration: 'none'
            }"
            >切换密码登录</a
          >
        </div>

        <el-form :model="form" ref="form" :rules="rules">
          <el-form-item prop="account">
            <el-input
              v-model="form.account"
              placeholder="请输入手机号"
              :style="{ width: '100%' }"
            ></el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <Captcha v-model="form.captcha" ref="captcha" />
          </el-form-item>
          <el-form-item prop="code">
            <div style="position: relative">
              <el-input v-model="form.code" placeholder="请输入短信验证码">
              </el-input>
              <el-button
                type="text"
                @click="sendCode"
                :disabled="isSendingCode"
                style="
                  position: absolute;
                  right: 10px;
                  top: 50%;
                  transform: translateY(-50%);
                "
              >
                {{ isSendingCode ? `${countdown}s` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              :style="{
                width: '100%',
                fontSize: '16px',
                padding: '12px'
              }"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <!-- <div style="text-align: right; margin-top: 20px">
          <a
            @click="goToForgotPassword"
            :style="{
              color: '#666',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }"
            >找回密码</a
          >
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import Slogo from './slogo.vue'
import Captcha from './captcha.vue'
import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'
import { setToken } from 'kit/helpers/token'
import { setSupplierUserProfile } from './context'
const client = makeClient()
const COUNTDOWN_SECONDS = 60

export default {
  name: 'LoginWithCaptcha',
  components: {
    Slogo,
    Captcha
  },
  data() {
    return {
      form: {
        account: '',
        code: '',
        otpToken: '',
        captcha: {
          token: '',
          value: ''
        },
        type: 'SUPPLIER',
        smsLogin: true
      },
      rules: {
        account: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        code: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' }
        ],
        captcha: [
          {
            validator: (rule, value, callback) => {
              if (!value || !value.value) {
                callback(new Error('请输入图形验证码'))
                return
              }

              callback()
            },
            trigger: 'blur'
          }
        ]
      },
      isSendingCode: false,
      countdown: COUNTDOWN_SECONDS,
      timer: null
    }
  },
  async created() {
    this.checkCountdown()

    var domain = window.location.host
    if (domain.includes('localhost')) {
      domain = 'labor.test.com'
    }
    const [err1, r1] = await client.getDomainInfo({
      body: {
        domain
      }
    })
    if (err1) {
      handleError(err1)
      return
    }

    localStorage.setItem('domainInfo', JSON.stringify(r1.data))
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    async handleLogin() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      const [err, r] = await client.login({
        body: {
          account: this.form.account,
          code: this.form.code,
          otpToken: this.form.otpToken,
          captchaToken: this.form.captcha.token,
          captchaAnswer: this.form.captcha.value,
          type: this.form.type,
          smsLogin: true
        }
      })
      if (err) {
        handleError(err)
        this.$refs.captcha.refreshCaptcha()
        return
      }

      setToken(r.data.token)

      setToken(r.data.token)

      const [err2, r2] = await client.supplierProfile()
      if (err2) {
        handleError(err2)
        return
      }
      setSupplierUserProfile(r2.data)
      //因为进入login页面时候没有加载菜单，导致跳转不过去
      await this.$parent.loadNavigations()

      this.$router.push('/roles')
    },
    async sendCode() {
      const noValid = await this.$refs.form.validateField()
      if (noValid) {
        return
      }

      const [err, r] = await client.sendLoginSms({
        body: {
          receiver: this.form.account,
          captchaToken: this.form.captcha.token,
          captchaAnswer: this.form.captcha.value
        }
      })

      if (err) {
        handleError(err)
        this.$refs.captcha.refreshCaptcha()
        return
      }
      this.form.otpToken = r.data.token

      this.startCountdown()
    },
    startCountdown() {
      const endTime = Date.now() + COUNTDOWN_SECONDS * 1000
      localStorage.setItem('smsCountdownEndTime', endTime)
      this.isSendingCode = true

      this.timer = setInterval(() => {
        const now = Date.now()
        const remaining = Math.round((endTime - now) / 1000)

        if (remaining <= 0) {
          clearInterval(this.timer)
          this.isSendingCode = false
          this.countdown = COUNTDOWN_SECONDS
          localStorage.removeItem('smsCountdownEndTime')
        } else {
          this.countdown = remaining
        }
      }, 1000)
    },
    checkCountdown() {
      const endTime = localStorage.getItem('smsCountdownEndTime')
      if (endTime) {
        const now = Date.now()
        const remaining = Math.round((endTime - now) / 1000)

        if (remaining > 0) {
          this.isSendingCode = true
          this.countdown = remaining
          this.startCountdown()
        }
      }
    },
    switchLoginType() {
      this.$router.push({ path: '/login' })
    },
    goToLogin() {
      this.$router.push({ path: '/login' })
    },
    goToRegister() {
      this.$router.push({ path: '/register' })
    },
    goToForgotPassword() {
      this.$router.push({ path: '/findPassword' })
    },
    goToWebsite() {
      // todo: Implement navigation to the main website
      console.log('Navigate to the official website')
    }
  }
}
</script>
