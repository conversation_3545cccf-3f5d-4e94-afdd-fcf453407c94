<template>
  <div>
    <el-dialog
      title="批量导入"
      :visible.sync="visible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <!-- 初始上传界面 -->
      <div v-if="!showErrorResult">
        <div style="text-align: center; margin-bottom: 20px">
          <div style="margin-bottom: 20px">
            <span style="color: #999; font-size: 14px">
              说明：点击下载导入模板后填写，文件不超过5M，
            </span>
            <br>
            <span style="color: #999; font-size: 14px">
              所属作业主体、所属客户、所属服务合同支持输入多个，个人手机号需保持唯一。
            </span>
          </div>
        </div>



        <div style="border-radius: 6px; margin: 16px 0; text-align: center">
          <el-upload
            width="100%"
            :headers="headerToken"
            ref="upload"
            class="upload"
            drag
            :data="uploadData"
            :file-list="fileList"
            :auto-upload="false"
            :on-success="onSuccess"
            :on-change="onChange"
            :on-error="onError"
            :on-remove="onRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="onExceed"
            accept=".xls, .xlsx"
            :action="uploadUrl"
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 30px 0 24px;
              "
            >
              <img src="../../../../assets/images/upload-excel.svg" alt="" />
              <el-button
                style="
                  width: 88px;
                  margin: 10px 0 8px;
                  border: 1px solid #cad0dbff;
                  color: #1e2228;
                  display: flex;
                  justify-content: center;
                "
                plain
                >选择文件</el-button
              >
              <div style="color: #828b9b; font-size: 14px">
                支持xlsx和xls文件，文件大小不超过5M
              </div>
            </div>
          </el-upload>
        </div>
      </div>

      <!-- 错误结果界面 -->
      <div v-else style="text-align: center; padding: 30px 20px">
        <div style="margin-bottom: 20px">
          <span style="font-size: 16px; color: #333">
            校验通过人员上传完成，校验未通过人员请导出错误数据处理。
          </span>
        </div>
        <el-button  style="margin-top: 20px;" @click="exportErrorData">
          导出错误数据
        </el-button>
      </div>

      <span style="display: flex;justify-content: center" slot="footer" class="dialog-footer" v-if="!showErrorResult">
        <el-button
          style="color: #1e2228; font-weight: 400"
          type="plain"
          @click="close"
          >取消</el-button
        >
        <el-button
          type="plain"
          @click="downloadTemplate"
          >下载导入模板</el-button
        >
        <el-button
          type="primary"
          @click="handleImport"
          :loading="loading"
          >确定</el-button
        >
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button
          style="font-weight: 400"
          type="primary"
          @click="close"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import { getToken } from '../../../../helpers/token'

export default {
  data() {
    return {
      visible: false,
      loading: false,
      fileList: [],
      showErrorResult: false,
      errorUuid: '',
      uploadData: {},
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env?.apiPath}/api/supplier/labor/download/check`
    }
  },
  methods: {
    open() {
      this.visible = true
      this.resetState()
    },

    close() {
      // 安全地清理上传组件
      if (this.$refs.upload && typeof this.$refs.upload.clearFiles === 'function') {
        this.$refs.upload.clearFiles()
      }
      this.resetState()
      this.visible = false
    },

    resetState() {
      this.showErrorResult = false
      this.errorUuid = ''
      this.fileList = []
      this.loading = false
    },

    async downloadTemplate() {
      try {
        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/supplier/labor/download/template`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '人员信息导入模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载模板失败：', error)
        this.$message.error('下载模板失败')
      }
    },

    handleImport() {
      if (!this.fileList.length) {
        this.$message.error('请上传文件')
        return
      }
      this.loading = true
      this.fileList[0].status = 'ready'
      this.$refs.upload.submit()
    },

    beforeUpload(file) {
      // 检查文件大小（5MB = 5 * 1024 * 1024 bytes）
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }

      // 检查文件格式
      const isExcel = file.type === 'application/vnd.ms-excel' ||
                     file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件!')
        return false
      }

      return true
    },

    onExceed(files, fileList) {
      this.$message.warning('只能上传一个文件，请先删除已上传的文件再重新上传')
    },

    onChange(file, fileList) {
      this.fileList = fileList
    },

    onRemove(file, fileList) {
      this.fileList = fileList
    },

    onSuccess(response, file, fileList) {
      this.loading = false

      if (response && response.success) {
        const { successCount, failCount, uuid } = response.data

        // 根据后端逻辑调整判断条件
        if (failCount === 0) {
          // 全部成功 - 注意后端successCount可能为0，但failCount为0表示全部成功
          const actualSuccessCount = successCount || '所有'
          this.$message.success(`成功导入${actualSuccessCount}条数据`)
          this.close()
          this.$emit('refresh')
        } else if (failCount > 0) {
          // 有失败的数据，显示错误结果界面
          if (uuid) {
            this.showErrorResult = true
            this.errorUuid = uuid
          } else {
            // 如果没有uuid但有失败数据，直接显示错误信息
            this.$message.error(`导入失败，共${failCount}条数据有问题`)
          }
        }
      } else {
        handleError(response)
      }
    },

    onError(err, file, fileList) {
      this.loading = false
      console.error('文件上传错误：', err)

      // 检查是否是后端服务器错误
      if (err && (
        (err.message && err.message.includes('ServletOutputStream')) ||
        (err.response && err.response.status === 500)
      )) {
        handleError(err)
      }
    },

    async exportErrorData() {
      try {
        // 使用fetch API携带token下载错误文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/supplier/labor/importVerifyErrorLog/${this.errorUuid}`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '导入错误数据.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        // this.$message.success('错误数据导出成功')
      } catch (error) {
        console.error('导出错误数据失败：', error)
        this.$message.error('导出错误数据失败')
      }
    }
  }
}
</script>

<style scoped>
.upload {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

/* 文件列表居中显示 */
.upload ::v-deep .el-upload-list {
  text-align: center;
}

.upload ::v-deep .el-upload-list__item {
  text-align: center;
  margin: 0 auto;
}

.upload ::v-deep .el-upload-list__item-name {
  text-align: center;
  display: block;
  width: 100%;
}
</style>
