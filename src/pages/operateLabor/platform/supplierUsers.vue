<template>
  <div
    class="users"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
      "
      label-position="right"
      label-width="120px"
    >
      <div style="display: flex; flex-wrap: wrap">
        <el-form-item
          label="用户名称/手机号"
          style="margin-right: 20px; margin-bottom: 10px"
        >
          <el-input
            v-model="conditions.filters.nameOrCellphone"
            placeholder="请输入用户名称/手机号"
            style="width: 280px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="角色名称" style="margin-bottom: 10px">
          <RolesSelector
            ref="rolesSelector"
            :multiple="false"
            v-model="conditions.filters.roleId"
            style="width: 280px"
          />
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        新建用户
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="name"
        label="姓名"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="cellphone"
        label="手机号"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="roles"
        label="关联角色"
        width="200"
        :formatter="formatRoles"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="modifyTime"
        label="更新时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.disabled ? 'danger' : 'success'"
            size="small"
          >
            {{ scope.row.disabled ? '已禁用' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            编辑/查看
          </el-button>
          <el-button
            v-if="!scope.row.disabled"
            type="text"
            size="small"
            @click="handleToggleStatus(scope.row, true)"
            >禁用</el-button
          >
          <el-button
            v-if="scope.row.disabled"
            type="text"
            size="small"
            @click="handleToggleStatus(scope.row, false)"
            >启用</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import handleSuccess from 'kit/helpers/handleSuccess'
import makeClient from 'kit/services/operateLabor/makeClient'
import RolesSelector from './selector/roles.vue'
const client = makeClient()

export default {
  components: {
    RolesSelector
  },
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          nameOrCellphone: '',
          roleId: null
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    if (this.$route.query.roleId) {
      this.conditions.filters.roleId = this.$route.query.roleId * 1
    }

    await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          nameOrCellphone: '',
          roleId: 0
        }
      }
      this.$refs.rolesSelector.reset()
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const payload = {
        ...this.conditions
      }

      const [err, r] = await client.supplierGetMembers({
        body: payload
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    formatRoles(row, column, cellValue) {
      if (!cellValue || cellValue.length === 0) return '-'
      return cellValue.map(role => role.name).join(', ')
    },

    handleAdd() {
      this.$router.push('/supplierUsers/new')
    },

    handleEdit(row) {
      const roleIds = row.roles.map(role => role.id).join(',')
      this.$router.push(
        `/supplierUsers/${row.memberId}/edit?name=${row.name}&cellphone=${row.cellphone}&roleIds=${roleIds}`
      )
    },

    async handleToggleStatus(row, disabled) {
      const action = disabled ? '禁用' : '启用'
      try {
        await this.$confirm(`确定要${action}用户 "${row.name}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const [err] = await client.supplierDisableMember({
          body: { id: row.memberId, disabled }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess(`${action}成功`)
        this.getList()
      } catch (error) {
        // User cancelled, do nothing
      }
    },

    async handleDelete(row) {
      try {
        await this.$confirm(
          `确定要删除用户 "${row.name}" 吗? 此操作无法撤销。`,
          '警告',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error'
          }
        )

        const [err] = await client.supplierRemoveMember({
          body: { id: row.memberId, disabled: true }
        })
        if (err) {
          handleError(err)
          return
        }
        handleSuccess('删除成功')
        this.getList()
      } catch (error) {
        // User cancelled, do nothing
      }
    }
  }
}
</script>
