<template>
  <div class="payrolls-new-container">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 600px"
    >
      <el-form-item label="客户" prop="customerId">
        <SupplierCustomersSelector
          :multiple="false"
          v-model="form.customerId"
          placeholder="请选择客户"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="作业主体" prop="supplierCorporationId">
        <CorporationsSelector
          v-model="form.supplierCorporationId"
          placeholder="请选择作业主体"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="服务合同" prop="contractId">
        <ServiceContractsSelector
          v-model="form.contractId"
          placeholder="请选择服务合同"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="税款所属期" prop="taxPeriod">
        <el-date-picker
          v-model="form.taxPeriod"
          type="month"
          placeholder="选择月份"
          format="yyyy-MM"
          value-format="yyyy-MM"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="个税申报月">
        <el-input :value="taxDeclarationMonth" readonly></el-input>
      </el-form-item>
      <el-form-item label="上传应发文件" prop="file">
        <input
          ref="fileInput"
          type="file"
          @change="handleFileChange"
          style="display: none"
          accept=".xlsx, .xls"
        />
        <div style="display: flex; align-items: center; width: 100%">
          <el-input
            :value="form.file ? form.file.name : ''"
            placeholder="请选择文件"
            readonly
            style="flex-grow: 1"
          >
            <el-button slot="append" @click="triggerFileInput"
              >选择文件</el-button
            >
          </el-input>
          <el-button
            type="text"
            @click="downloadTemplate"
            style="margin-left: 10px"
            >下载模板</el-button
          >
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading"
          >创建</el-button
        >
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import CorporationsSelector from './selector/corporations.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'

const client = makeClient()

// 获取上一个月的 YYYY-MM 格式字符串
function getPreviousMonth() {
  const date = new Date()
  date.setMonth(date.getMonth() - 1)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

// 获取当前月的 YYYY-MM 格式字符串
function getCurrentMonth() {
  const date = new Date()
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

export default {
  name: 'PayrollsNew',
  components: {
    SupplierCustomersSelector,
    CorporationsSelector,
    ServiceContractsSelector
  },
  data() {
    return {
      loading: false,
      form: {
        customerId: null,
        supplierCorporationId: null,
        contractId: null,
        taxPeriod: getPreviousMonth(),
        file: null // This will hold the File object
      },
      rules: {
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        contractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        taxPeriod: [
          { required: true, message: '请选择税款所属期', trigger: 'change' }
        ],
        file: [{ required: true, message: '请上传应发文件', trigger: 'blur' }]
      }
    }
  },
  computed: {
    taxDeclarationMonth() {
      return getCurrentMonth()
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        this.form.file = file
        this.$refs.form.validateField('file')
      }
    },
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) return

      this.loading = true
      try {
        const formData = new FormData()
        formData.append('customerId', this.form.customerId)
        formData.append(
          'supplierCorporationId',
          this.form.supplierCorporationId
        )
        formData.append('contractId', this.form.contractId)
        formData.append('taxPeriod', this.form.taxPeriod)
        formData.append('file', this.form.file)

        const [err, response] = await client.supplierSalaryAddPayroll({
          body: formData
        })

        if (err) {
          handleError(err)
          return
        }
        this.$message.success('创建成功')
        this.$router.push('/payrolls')
      } finally {
        this.loading = false
      }
    },
    downloadTemplate() {
      // This should point to your actual template file URL
      const templateUrl = '/path/to/your/template.xlsx'
      window.open(templateUrl, '_blank')
    }
  }
}
</script>

<style scoped>
.payrolls-new-container {
}
</style>
