<template>
  <div class="billingManageDetail" style="height: 100%">
    <div style="display: flex; margin: 20px 0">
      <div
        style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 100px;
          background: #4f71ff;
          color: #fff;
          border-radius: 10px;
        "
      >
        <span>{{ formatterMonth(info.billMonth) }}</span>
        <span>账单月份</span>
      </div>
      <div
        style="
          display: flex;
          flex-direction: column;
          margin-left: 20px;
          justify-content: space-around;
        "
      >
        <span>{{ info.customerName }}</span>
        <span>所属服务合同：{{ info.contractName }}</span>
      </div>
    </div>
    <el-table
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="feeTypeDesc"
        label="结算项目"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="billMonth"
        label="结算月份"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="personCount"
        label="办理人数"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="totalAmount"
        label="应收金额"
        min-width="160"
      ></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      info: {
        billMonth: '',
        customerName: '',
        contractName: ''
      },
      tableData: []
    }
  },
  computed: {
    billId() {
      return this.$route.params.id
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      const [err, r] = await client.apiSupplierBillsBillId({
        method: 'GET',
        params: `id=${this.billId}`
      })
      if (err) {
        handleError(err)
        return
      }
      this.info.billMonth = r.data.billMonth
      this.info.customerName = r.data.customerName
      this.info.contractName = r.data.contractName
      this.tableData = r.data.categories || []
    },
    formatterMonth(value) {
      const year = value.split('-')[0]
      const month = value.split('-')[1]
      return value ? month + ' / ' + year : ''
    },
    handleView(row) {
      const { billMasterId, feeType } = row
      if (feeType === 'SALARY') {
        this.$router.push({
          path: '/billingManage/salary',
          query: { billMasterId: billMasterId }
        })
      } else if (feeType === 'MANAGEMENT_FEE') {
        this.$router.push({
          path: '/billingManage/managementFee',
          query: { billMasterId: billMasterId }
        })
      } else {
        this.$router.push({
          path: '/billingManage/otherFee',
          query: { billMasterId: billMasterId }
        })
      }
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const [err] = await client.updateTemplateStatus({
              body: {
                tempId: row.tempId,
                status: 'DELETED'
              }
            })

            if (err) {
              handleError(err)
              return
            }

            handleSuccess('删除成功')
            // 刷新列表
            await this.getList()
          } catch (error) {
            handleError(error)
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped></style>
