<template>
  <div class="supplierCustomersSelector">
    <el-select
      v-model="selectedCustomerIds"
      :multiple="multiple"
      filterable
      clearable
      remote
      :remote-method="remoteMethod"
      placeholder="请输入客户名称搜索"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      @change="handleChange"
    >
      <el-option
        v-for="item in customers"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <span style="float: left">{{ item.name }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.shortName }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'SupplierCustomersSelector',
  props: {
    multiple: {
      type: <PERSON>olean,
      default: true
    },
    value: {
      type: [Array, Number],
      default() {
        return this.multiple ? [] : null
      }
    }
  },
  data() {
    return {
      loading: false,
      customers: [], // Customers from search results
      selectedCustomerIds: this.multiple ? [] : null
    }
  },
  async created() {
    await this.fetchCustomers()

    if (!this.value) {
      return
    }

    // Handle initial value
    if (this.multiple && Array.isArray(this.value)) {
      // Convert to numbers for consistency
      const customerIds = this.value.map(id => Number(id))
      if (customerIds.length > 0) {
        await this.fetchCustomersByIds(customerIds)
        this.selectedCustomerIds = customerIds
      }
    } else if (!this.multiple && this.value) {
      const customerId = Number(this.value)
      await this.fetchCustomersByIds([customerId])
      this.selectedCustomerIds = customerId
    }
  },
  methods: {
    reset() {
      this.selectedCustomerIds = this.multiple ? [] : null
    },
    async fetchCustomers(name = '') {
      this.loading = true
      const [err, r] = await client.supplierListCustomer({
        body: {
          start: 0,
          offset: 0,
          limit: 1000,
          sorts: [],
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            name: name
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      // Merge new results with existing ones, avoiding duplicates
      if (r.data && r.data.list) {
        for (const customer of r.data.list) {
          if (!this.customers.find(item => item.id === customer.id)) {
            this.customers.push(customer)
          }
        }
      }
    },
    async fetchCustomersByIds(customerIds) {
      if (!customerIds || customerIds.length === 0) return

      // Filter out customers that are already loaded
      const missingIds = customerIds.filter(id =>
        !this.customers.find(customer => customer.id === id)
      )

      if (missingIds.length === 0) return

      // Fetch customers by IDs using the filters.customerIds parameter
      const [err, r] = await client.supplierListCustomer({
        body: {
          start: 0,
          offset: 0,
          limit: 1000,
          sorts: [],
          withTotal: false,
          withDisabled: true,
          withDeleted: true,
          filters: {
            customerIds: missingIds
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }

      // Add fetched customers to the list
      if (r.data && r.data.list) {
        for (const customer of r.data.list) {
          if (!this.customers.find(item => item.id === customer.id)) {
            this.customers.push(customer)
          }
        }
      }
    },
    remoteMethod(query) {
      this.fetchCustomers(query)
    },
    handleChange(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped>
.supplierCustomersSelector {
  width: 100%;
}
</style>