<template>
  <el-dialog
    title="选择模板"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="template-dialog">
      <!-- 搜索区域 -->
      <el-form :inline="true" class="search-form">
        <el-form-item label="模板名称">
          <el-input
            v-model="searchName"
            placeholder="请输入模板名称"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 模板列表 -->
      <el-table
        v-loading="loading"
        :data="templates"
        style="width: 100%; margin-top: 20px"
        highlight-current-row
        @current-change="handleCurrentChange"
        height="400px"
      >
        <el-table-column
          prop="tempName"
          label="模板名称"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.tempStatus)" size="small">
              {{ getStatusText(scope.row.tempStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="modifiedTime"
          label="更新时间"
          width="180"
          :formatter="formatDateTime"
        />
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
        style="text-align: right; margin-top: 20px"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :disabled="!selectedTemplate">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'TemplateDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      templates: [],
      selectedTemplate: null,
      searchName: '',
      searchStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.loadTemplates()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadTemplates() {
      this.loading = true
      const [err, r] = await client.getTemplateList({
        body: {
          offset: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize,
          withTotal: true,
          withDisabled: true,
          withDeleted: false,
          filters: {
            tempName: this.searchName,
            tempStatus: this.searchStatus
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.templates = r.data?.list || []
      this.total = r.data?.total || 0
    },
    handleSearch() {
      this.currentPage = 1
      this.loadTemplates()
    },
    handleReset() {
      this.searchName = ''
      this.searchStatus = ''
      this.currentPage = 1
      this.loadTemplates()
    },
    handlePageChange(page) {
      this.currentPage = page
      this.loadTemplates()
    },
    handleCurrentChange(row) {
      this.selectedTemplate = row
    },
    handleConfirm() {
      if (this.selectedTemplate) {
        this.$emit('confirm', this.selectedTemplate)
        this.handleCancel()
      }
    },
    handleCancel() {
      this.dialogVisible = false
      this.selectedTemplate = null
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },
    getStatusType(status) {
      const statusMap = {
        DRAFT: 'info',
        ERROR: 'danger',
        ENABLED: 'success',
        DISABLED: 'warning'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        DRAFT: '草稿',
        ERROR: '错误',
        ENABLED: '启用',
        DISABLED: '停用'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.template-dialog {
  padding: 0;
}

.search-form {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}
</style>
