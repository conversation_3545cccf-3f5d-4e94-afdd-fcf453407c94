<template>
  <div class="supplierLaborsSelector">
    <el-select
      v-model="selectedLaborIds"
      :multiple="multiple"
      filterable
      clearable
      remote
      :remote-method="remoteMethod"
      placeholder="请输入人员名称搜索"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      @change="handleChange"
    >
      <el-option
        v-for="item in labors"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <span style="float: left">{{ item.name }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{
          item.shortName
        }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'SupplierLaborsSelector',
  props: {
    multiple: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    value: {
      type: [Array, String, Number],
      default() {
        return this.multiple ? [] : null
      }
    }
  },
  data() {
    return {
      loading: false,
      labors: [], // Labors from search results
      selectedLaborIds: this.multiple ? [] : null
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (this.multiple) {
          const laborIds = (newValue || []).map(id => Number(id))
          this.selectedLaborIds = laborIds
          if (laborIds.length > 0) {
            this.fetchLaborsByIds(laborIds)
          }
        } else {
          const laborId = newValue ? Number(newValue) : null
          this.selectedLaborIds = laborId
          if (laborId) {
            this.fetchLaborsByIds([laborId])
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.fetchLabors()
  },
  methods: {
    reset() {
      this.selectedLaborIds = this.multiple ? [] : null
    },
    async fetchLabors(name = '') {
      this.loading = true
      const [err, r] = await client.supplierLaborList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            name: name
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      if (r.data && r.data.list) {
        for (const labor of r.data.list) {
          if (!this.labors.find(item => item.id === labor.id)) {
            this.labors.push(labor)
          }
        }
      }
    },
    async fetchLaborsByIds(laborIds) {
      if (!laborIds || laborIds.length === 0) return

      const missingIds = laborIds.filter(
        id => !this.labors.find(labor => labor.id === id)
      )

      if (missingIds.length === 0) return

      const [err, r] = await client.supplierLaborList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: true,
          withDeleted: true,
          filters: {
            ids: missingIds
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }

      if (r.data && r.data.list) {
        for (const labor of r.data.list) {
          if (!this.labors.find(item => item.id === labor.id)) {
            this.labors.push(labor)
          }
        }
      }
    },
    remoteMethod(query) {
      this.fetchLabors(query)
    },
    handleChange(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped>
.supplierLaborsSelector {
  width: 100%;
}
</style>
