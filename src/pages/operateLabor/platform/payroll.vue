<template>
  <div
    class="payroll-detail-container"
    v-loading="loading"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- 摘要信息 -->
    <div
      class="summary-container"
      style="flex: 0 0 auto; padding: 20px; background: var(--o-primary-bg-color); border-radius: 5px; margin-bottom: 20px; font-size: 14px; color: #606266;"
    >
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">客户：</label>
            <span>{{ summaryData.customerName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">服务合同：</label>
            <span>{{ summaryData.contractName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">作业主体：</label>
            <span>{{ summaryData.supplierCorporationName }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">税款所属期：</label>
            <span>{{ summaryData.taxPeriod }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总人数：</label>
            <span>{{ summaryData.totalPeople }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总应发金额：</label>
            <span>{{ summaryData.totalPayable }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总实发金额：</label>
            <span>{{ summaryData.netPaymentTotal }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 工资明细列表 -->
    <el-table
      :data="tableData"
      size="small"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column prop="name" label="姓名" width="120"></el-table-column>
      <el-table-column
        prop="idCard"
        label="身份证"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="phoneNumber"
        label="手机号"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="payableAmount"
        label="应发金额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="netPayment"
        label="实发金额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedIncome"
        label="累计收入"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedTaxableAmount"
        label="累计应纳税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedPrepaidTax"
        label="累计已预缴税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="currentTaxAmount"
        label="本期应预扣预缴税额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="currentWithholdingTax"
        label="本月已预扣预缴税额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="currentWithholdingTax"
        label="本次应预扣预缴税额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="vatAmount"
        label="增值税额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="urbanConstructionTax"
        label="城市维护建设稅"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="educationSurcharge"
        label="教育费附加"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="localEducationSurcharge"
        label="地方教育附加"
        width="140"
      ></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="conditions.total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'PayrollDetail',
  data() {
    return {
      loading: true,
      payrollId: null,
      summaryData: {},
      tableData: [],
      conditions: {
        limit: 10,
        offset: 0,
        total: 0
      }
    }
  },
  created() {
    this.payrollId = this.$route.params.id
    this.summaryData = this.$route.query
    this.loadDetails()
  },
  methods: {
    async loadDetails() {
      this.loading = true
      try {
        const params = {
          id: this.payrollId,
          limit: this.conditions.limit,
          offset: this.conditions.offset
        }
        const [err, r] = await client.supplierSalaryListPayrollDetail({
          body: params
        })
        if (err) {
          handleError(err)
          return
        }
        this.tableData = r.data.list || []
        this.conditions.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadDetails()
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped></style>
